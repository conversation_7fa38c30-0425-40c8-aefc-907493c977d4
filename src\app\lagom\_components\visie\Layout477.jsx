"use client";

import { Button } from "@relume_io/relume-ui";
import React from "react";

export function Layout477() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="grid grid-cols-1 items-center gap-12 md:grid-cols-2 md:gap-x-16">
          <div className="relative flex">
            <div className="absolute top-[10%] left-0 w-2/5">
              <img
                src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image-dim.png"
                className="aspect-[3/2] size-full rounded-image object-cover"
                alt="Relume placeholder image 1"
              />
            </div>
            <div className="absolute right-0 bottom-[10%] w-[35%]">
              <img
                src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image-dim.png"
                className="aspect-square size-full rounded-image object-cover"
                alt="Relume placeholder image 2"
              />
            </div>
            <div className="mx-[10%]">
              <img
                src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
                className="aspect-square size-full rounded-image object-cover"
                alt="Relume placeholder image 3"
              />
            </div>
          </div>
          <div>
            <h2 className="mb-5 text-5xl font-semibold md:mb-6 md:text-7xl lg:text-8xl">
              Onze visie
            </h2>
            <p className="md:text-md">
              Bij Forest Forward geloven we dat lokale natuurcreatie voor elk
              bedrijf een duidelijke win-win kan zijn. Daarom zeggen we ook
              altijd dat we veel méér doen dan bomen planten. Natuur creëren, is
              mensen connecteren. We brengen bedrijven, collega’s, scholen,
              overheden en de lokale omgeving samen rond natuurprojecten die
              verbinden en enthousiasmeren. We halen duurzaamheid uit de
              directiezaal. We maken het tastbaar door er een meetbare impact en
              langdurige betrokkenheid aan te koppelen. Of het nu gaat om een
              bedrijfsbos, schoolbos of voedselbos, samen zorgen we voor
              waardevolle natuur die de biodiversiteit versterkt en de toekomst
              voor ons allen verbetert.
            </p>
            <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
              <Button title="Welke natuur wil jij creëren?" variant="secondary">
                Welke natuur wil jij creëren?
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

"use client";

import React from "react";

export function Header145_1() {
  return (
    <section id="relume">
      <div className="px-[5%] py-16 md:py-24 lg:py-28">
        <div className="container max-w-lg">
          <div className="flex w-full flex-col items-center text-center">
            <h1 className="mb-5 text-6xl font-semibold md:mb-6 md:text-9xl lg:text-10xl">
              Duurzaamheidsrapporten
            </h1>
            <p className="md:text-md">
              Een duurzaamheidsrapport volgens de officiële normen, daar kruipt
              veel tijd en energie in. Daarom is het een must dat je rapport ook
              effectief gelezen wordt. En daar helpen we je graag mee. We doen
              niet de technische kant van het verhaal − daarvoor werken we samen
              met eigen partners of met die van jullie. Maar onze mensen zijn
              mee met de CSRD-regelgeving, kunnen die lezen en interpreteren, en
              kunnen dus vooral de techniciteit omzetten in een heldere taal. Zo
              wordt je rapport ook effectief lezenswaardig. Naast de technische
              output zorgen we ook voor extra creatieve output - gaande van
              sterke interviews tot reportages over je medewerkers, enz. -
              waardoor jouw rapport menselijker en toegankelijker wordt.
              Bovendien werken we met designers die meer dan tien jaar
              professionele ervaring hebben als magazinemakers.   Dit doen we
              onder meer voor spelers zoals Revive, die recent nog een Impact
              Report naar buiten bracht.  
            </p>
          </div>
        </div>
      </div>
      <div>
        <img
          src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
          className="aspect-video size-full object-cover"
          alt="Relume placeholder image"
        />
      </div>
    </section>
  );
}

"use client";

import { But<PERSON> } from "@relume_io/relume-ui";
import React, { Fragment } from "react";

export function Stats41() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="mb-12 grid grid-cols-1 gap-y-5 md:mb-18 md:grid-cols-2 md:gap-x-12 lg:mb-20 lg:gap-x-20">
          <div>
            <h2 className="text-5xl font-semibold md:text-7xl lg:text-8xl">
              Onze visie
            </h2>
          </div>
          <div>
            <p className="md:text-md">
              Echte verbinding krijg je wanneer mensen samen betekenisvolle
              momenten beleven. Bij Lagom geloven we daarom in duurzame events.
              Die versterken teams en maken elk contact échter. Daarom
              combineren we beleving, samenwerking en zorgeloze organisatie in
              unieke ervaringen die blijven hangen. Zonder daarbij de fun-factor
              uit het oog te verliezen.
            </p>
            <div className="mt-6 flex flex-wrap items-center gap-4 md:mt-8">
              <Button title="Ontdek onze visie en aanpak" variant="secondary">
                Ontdek onze visie en aanpak
              </Button>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          <Fragment>
            <div className="p-8 first:flex first:flex-col first:md:col-span-2 first:md:row-span-1 first:lg:col-span-1 first:lg:row-span-2 [&:nth-last-child(2)]:order-last [&:nth-last-child(2)]:md:order-none">
              <h3 className="mb-8 text-md leading-[1.4] font-semibold md:mb-10 md:text-xl lg:mb-12">
                Partnerships
              </h3>
              <p className="text-right text-10xl leading-[1.3] font-semibold md:text-[4rem] lg:text-[5rem] mt-auto">
                26
              </p>
              <div className="my-4 h-px w-full bg-border-primary" />
              <p className="text-right">
                Partnerships met sociale organisaties
              </p>
            </div>
          </Fragment>
          <Fragment>
            <div>
              <img
                className="aspect-[3/2] size-full rounded-image object-cover"
                src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
                alt="Relume placeholder image"
              />
            </div>
          </Fragment>
          <Fragment>
            <div className="p-8 first:flex first:flex-col first:md:col-span-2 first:md:row-span-1 first:lg:col-span-1 first:lg:row-span-2 [&:nth-last-child(2)]:order-last [&:nth-last-child(2)]:md:order-none">
              <h3 className="mb-8 text-md leading-[1.4] font-semibold md:mb-10 md:text-xl lg:mb-12">
                Partnerships
              </h3>
              <p className="text-right text-10xl leading-[1.3] font-semibold md:text-[4rem] lg:text-[5rem]">
                26
              </p>
              <div className="my-4 h-px w-full bg-border-primary" />
              <p className="text-right">
                Partnerships met sociale organisaties
              </p>
            </div>
          </Fragment>
          <Fragment>
            <div className="p-8 first:flex first:flex-col first:md:col-span-2 first:md:row-span-1 first:lg:col-span-1 first:lg:row-span-2 [&:nth-last-child(2)]:order-last [&:nth-last-child(2)]:md:order-none">
              <h3 className="mb-8 text-md leading-[1.4] font-semibold md:mb-10 md:text-xl lg:mb-12">
                Partnerships
              </h3>
              <p className="text-right text-10xl leading-[1.3] font-semibold md:text-[4rem] lg:text-[5rem]">
                26
              </p>
              <div className="my-4 h-px w-full bg-border-primary" />
              <p className="text-right">
                Partnerships met sociale organisaties
              </p>
            </div>
          </Fragment>
          <Fragment>
            <div>
              <img
                className="aspect-[3/2] size-full rounded-image object-cover"
                src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
                alt="Relume placeholder image"
              />
            </div>
          </Fragment>
        </div>
      </div>
    </section>
  );
}

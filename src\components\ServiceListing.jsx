"use client";

import { motion, AnimatePresence } from "framer-motion";
import React, { useState, useEffect, useRef } from "react";
import Link from "next/link";

export function ServiceListing({
  services = [],
  className = "",
}) {
  const [hoveredService, setHoveredService] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isMobile, setIsMobile] = useState(false);
  const containerRef = useRef(null);

  const serviceData = services.length > 0 ? services : defaultServices;

  // Generate dynamic positions for background images based on service count
  const generateImagePositions = (serviceCount) => {
    const positions = [
      { top: '10%', left: '8%', rotation: 12, size: 'w-20 h-20 md:w-48 md:h-48' },
      { top: '15%', right: '12%', rotation: -8, size: 'w-16 h-16 md:w-36 md:h-36' },
      { top: '35%', left: '5%', rotation: 25, size: 'w-18 h-18 md:w-40 md:h-40' },
      { top: '45%', right: '8%', rotation: -15, size: 'w-14 h-14 md:w-32 md:h-32' },
      { top: '65%', left: '15%', rotation: 8, size: 'w-22 h-22 md:w-52 md:h-52' },
      { top: '75%', right: '6%', rotation: -20, size: 'w-16 h-16 md:w-36 md:h-36' },
      { top: '25%', left: '25%', rotation: 35, size: 'w-12 h-12 md:w-24 md:h-24' },
      { top: '55%', right: '25%', rotation: -12, size: 'w-14 h-14 md:w-28 md:h-28' },
    ];
    return positions.slice(0, serviceCount);
  };

  const imagePositions = generateImagePositions(serviceData.length);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Track mouse position for floating card (desktop only)
  useEffect(() => {
    if (isMobile) return;

    const handleMouseMove = (e) => {
      setMousePosition({
        x: e.clientX,
        y: e.clientY
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [isMobile]);

  return (
    <section
      ref={containerRef}
      className={`relative px-[5%] py-16 md:py-24 lg:py-28 bg-white overflow-hidden ${className}`}
    >
      {/* Dynamic Background Images */}
      <div className="absolute inset-0 pointer-events-none">
        {serviceData.map((service, index) => {
          const position = imagePositions[index];
          if (!position || !service.backgroundImage) return null;

          return (
            <motion.div
              key={`bg-${service.id}`}
              className={`absolute ${position.size} opacity-30`}
              style={{
                top: position.top,
                left: position.left,
                right: position.right,
                transform: `rotate(${position.rotation}deg)`,
              }}
              initial={{ opacity: 0, scale: 0.8, rotate: position.rotation - 15 }}
              animate={{ opacity: 0.3, scale: 1, rotate: position.rotation }}
              transition={{ duration: 1, delay: 0.2 + (index * 0.2) }}
            >
              <img
                src={service.backgroundImage}
                alt=""
                className="w-full h-full object-cover rounded-lg"
              />
            </motion.div>
          );
        })}
      </div>

      <div className="container relative z-10">


        {/* Service List */}
        <motion.div
          className="space-y-2 md:space-y-3"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={{
            hidden: {},
            visible: {
              transition: {
                staggerChildren: 0.1
              }
            }
          }}
        >
          {serviceData.map((service, index) => (
            <motion.div
              key={service.id}
              className={`group cursor-pointer py-4 md:py-6 text-center relative ${
                isMobile ? '' : 'hover:scale-105'
              }`}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              onMouseEnter={() => !isMobile && setHoveredService(service.id)}
              onMouseLeave={() => !isMobile && setHoveredService(null)}
              whileHover={!isMobile ? {
                scale: 1.05,
                transition: { duration: 0.3, ease: "easeOut" }
              } : {}}
            >
              {service.href ? (
                <Link href={service.href} className="block">
                  <h3 className={`text-4xl md:text-6xl lg:text-8xl xl:text-9xl font-semibold text-text-primary tracking-tight ${
                    isMobile
                      ? 'active:text-link transition-colors duration-200'
                      : 'group-hover:text-link transition-colors duration-300'
                  }`}>
                    {service.abstractTitle || service.title.toUpperCase()}
                  </h3>
                </Link>
              ) : (
                <h3 className={`text-4xl md:text-6xl lg:text-8xl xl:text-9xl font-semibold text-text-primary tracking-tight ${
                  isMobile
                    ? ''
                    : 'group-hover:text-link transition-colors duration-300'
                }`}>
                  {service.abstractTitle || service.title.toUpperCase()}
                </h3>
              )}

              {/* Short separator line */}
              {index < serviceData.length - 1 && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 md:w-20 h-px bg-border-25"></div>
              )}
            </motion.div>
          ))}
        </motion.div>

        {/* Floating Card that follows mouse (Desktop only) */}
        {!isMobile && (
          <AnimatePresence>
            {hoveredService && (
              <motion.div
                className="fixed z-[9999] pointer-events-none"
                style={{
                  left: mousePosition.x + 20,
                  top: mousePosition.y - 60,
                }}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{
                  opacity: 1,
                  scale: 1,
                  transition: { duration: 0.2 }
                }}
                exit={{
                  opacity: 0,
                  scale: 0.9,
                  transition: { duration: 0.2 }
                }}
              >
                <div className="bg-background-alternative text-text-alternative p-5 rounded-lg shadow-xlarge border border-border-alternative max-w-xs backdrop-blur-sm">
                  {(() => {
                    const service = serviceData.find(s => s.id === hoveredService);
                    return service ? (
                      <div>
                        <p className="text-sm leading-relaxed opacity-90">
                          {service.description}
                        </p>
                      </div>
                    ) : null;
                  })()}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </div>
    </section>
  );
}

import React from "react";
import { Navbar2 } from "./_components/Navbar";
import { Footer2 } from "../_components/Footer";
import { Logo3 } from "../../components/Logo3";
import { Stats41 } from "./_components/home/<USER>";
import { Cta13 } from "./_components/home/<USER>";
import { Blog40 } from "./_components/home/<USER>";
import { Cta7 } from "./_components/home/<USER>";
import { Layout394 } from "../../components/EcoLayout2";
import { Header114 } from "../../components/Header114";
import { ServiceListing } from "../../components/ServiceListing";
import { Gallery1 } from "@/components/Gallery1";

const galleryImages = [
  {
    src: "/images/forestforward/homepage/1.png",
    alt: "Moodboard image 1"
  },
  {
    src: "/images/forestforward/homepage/2.png",
    alt: "Moodboard image 2"
  },
  {
    src: "/images/forestforward/homepage/3.png",
    alt: "Moodboard image 3"
  },
  {
    src: "/images/forestforward/homepage/4.png",
    alt: "Moodboard image 4"
  },
  {
    src: "/images/forestforward/homepage/5.png",
    alt: "Moodboard image 5"
  },
  {
    src: "/images/forestforward/homepage/6.png",
    alt: "Moodboard image 6"
  },
  {
    src: "/images/forestforward/homepage/7.png",
    alt: "Moodboard image 7"
  },
  {
    src: "/images/forestforward/homepage/8.png",
    alt: "Moodboard image 8"
  },
  {
    src: "/images/forestforward/homepage/9.png",
    alt: "Moodboard image 9"
  }
];

const myServices = [
  {
    id: "bedrijfsbos",
    title: "Bedrijfsbos",
    abstractTitle: "BEDRIJFSBOS",
    description: "Een bedrijfsbos is een tastbaar symbool van duurzaam en maatschappelijk engagement. Het versterkt biodiversiteit, slaat CO₂ op, betrekt medewerkers via boomplantacties en verhoogt je aantrekkelijkheid als werkgever.",
    href: "/forestforward/bedrijfsbos",
    backgroundImage: "/images/forestforward/bedrijfsbos/3.png"
  },
  {
    id: "schoolbos",
    title: "Schoolbos",
    abstractTitle: "SCHOOLBOS",
    description: "Learning Leaves verbindt bedrijven en scholen via compacte, biodiverse schoolbossen die educatie, welzijn en ecologische impact combineren. Leerlingen leren over natuur en duurzaamheid, en het bos zorgt voor blijvende lokale impact en wordt wetenschappelijk opgevolgd.",
    href: "/forestforward/schoolbos",
    backgroundImage: "/images/forestforward/schoolbos/7.png"
  },
  {
    id: "voedselbos",
    title: "Voedselbos",
    abstractTitle: "VOEDSELBOS",
    description: "Een voedselbos combineert voedselproductie met biodiversiteit, bodemverbetering en klimaatadaptatie. Het versterkt lokale betrokkenheid, biedt tastbare maatschappelijke meerwaarde en creëert een inspirerende plek die je duurzame ambities zichtbaar maakt voor medewerkers en buurt.",
    href: "/forestforward/voedselbos",
    backgroundImage: "/images/forestforward/voedselbos/4.png"
  },
  {
    id: "natuuropwaardering",
    title: "Natuuropwaardering",
    abstractTitle: "NATUUROPWAARDERING",
    description: "Natuuropwaardering maakt van verwaarloosde groenzones biodiverse, toegankelijke natuur. Dit versterkt biodiversiteit, klimaatweerbaarheid en levenskwaliteit, en biedt bedrijven zichtbare maatschappelijke impact en inspirerende teambuilding.",
    href: "/forestforward/natuuropwaardering",
    backgroundImage: "/images/forestforward/natuuropwaardering/2.png"
  },
  {
    id: "dakboerderij",
    title: "Dakboerderij",
    abstractTitle: "DAKBOERDERIJ",
    description: "Een dakboerderij brengt natuur en lokale voedselproductie terug in de stad. Ze koelt, buffert water, versterkt biodiversiteit en creëert een groene plek voor educatie, beleving en gemeenschapsvorming. Zo bouw je samen aan een leefbare, duurzame stadsomgeving.",
    href: "/forestforward/dakboerderij",
    backgroundImage: "/images/forestforward/dakboerderij/5.png"
  },
  {
    id: "start2forest",
    title: "Start2Forest",
    abstractTitle: "START2FOREST",
    description: "Start2Forest biedt bedrijven de kans om met een beperkt aantal bomen bij te dragen aan een collectief bedrijfsbos. Al vanaf één boom toon je duurzaam engagement; vanaf vijftig mag je mee planten. Bomen zijn ook beschikbaar als groen relatiegeschenk in pakketten vanaf vijf dozen.",
    href: "/forestforward/start2forest",
    backgroundImage: "/images/forestforward/s2f/S2F 1.png"
  },
  {
    id: "boscompensatie",
    title: "Boscompensatie",
    abstractTitle: "BOSCOMPENSATIE",
    description: "Boscompensatie is een wettelijke plicht bij ontbossing, maar ook een kans om ecologie en economie te verzoenen. Forest Forward maakt er een duurzaam project van, met gronden, beheer en opvolging die echte natuurwaarde creëren en maatschappelijke meerwaarde opleveren.",
    href: "/forestforward/boscompensatie",
    backgroundImage: "/images/forestforward/boscompensatie/3.png"
  }
];

export default function Page() {
  return (
    <div>
      <Navbar2 />
      <Header114
        title = "We helpen je duurzaamheid uit<br>de directiekamer te halen."
        subtitle = "Samen maken we<br> duurzaamheid tastbaar."
        description = "Wil je meer doen dan enkel over je duurzame plannen praten? Heb je er nood aan om je interne duurzame inspanningen zichtbaarder te maken? Meer bevattelijk te maken? Dichter bij je mensen te brengen? Samen met je collega’s of andere stakeholders rollen we de mouwen op voor meer groen. We helpen je doordacht en dichtbij natuur te creëren die de biodiversiteit versterkt, een positieve impact heeft op de buurt en perfect aansluit bij jouw bedrijfswaarden. Zo groeien we samen naar een biodiversere, duurzame toekomst."
        backgroundImage = "/images/forestforward/homepage/4.png"
        primaryButtonText = "Ontdek meer"
        secondaryButtonText = "Contacteer ons"
        secondaryButtonHref = "/contact"
        tertiaryButtonText = "Contacteer ons"
        tertiaryButtonHref = "/contact"
      />
      <ServiceListing 
        services={myServices}
      />
      <Logo3 
        title="Deze bedrijven gingen je voor"
      />
      <Stats41 />
      <Cta13 />
      <Blog40 />
      <Cta7 />
      <Layout394 />
      <Gallery1
        images={galleryImages}
        title="Moodboard"
      />
      <Footer2 />
    </div>
  );
}

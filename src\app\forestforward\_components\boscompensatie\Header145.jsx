"use client";

import { Button } from "@relume_io/relume-ui";
import React from "react";

export function Header145() {
  return (
    <section id="relume">
      <div className="px-[5%] py-16 md:py-24 lg:py-28">
        <div className="container max-w-lg">
          <div className="flex w-full flex-col items-center text-center">
            <h1 className="mb-5 text-6xl font-semibold md:mb-6 md:text-9xl lg:text-10xl">
              Boscompensatie
            </h1>
            <p className="md:text-md">
              Ambitie, groei, expansie; het zit diepgeworteld in het DNA van
              elke ondernemer. We zien het liefst van al geen bos verdwijnen,
              maar soms zorgt de economische realiteit voor ingrepen in de
              natuur en moet er ontbost worden. In plaats van dan de ogen te
              sluiten of hard te roepen, zoeken we naar oplossingen. En dan komt
              boscompensatie in zicht, want dat is nog altijd beter dan niet
              compenseren en gewoon euro’s over te schrijven.  Bij Forest
              Forward zorgen we ervoor dat boscompensatie niet alleen een
              administratieve last is, maar ook een kans om duurzaam te
              investeren in nieuwe bossen.
            </p>
            <div className="mt-6 flex items-center justify-center gap-x-4 md:mt-8">
              <Button title="Ontdek meer">Ontdek meer</Button>
              <Button title="Contacteer ons" variant="secondary">
                Contacteer ons
              </Button>
            </div>
          </div>
        </div>
      </div>
      <div>
        <img
          src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
          className="aspect-video size-full object-cover"
          alt="Relume placeholder image"
        />
      </div>
    </section>
  );
}

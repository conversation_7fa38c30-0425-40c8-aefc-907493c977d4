"use client";

import React from "react";
import { BiEn<PERSON>ope, BiMap, BiPhone } from "react-icons/bi";

export function Contact17() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="grid auto-cols-fr grid-cols-1 gap-x-12 gap-y-12 md:grid-cols-3 md:gap-y-16">
          <div>
            <div className="mb-5 lg:mb-6">
              <BiEnvelope className="size-12" />
            </div>
            <h3 className="mb-3 text-2xl leading-[1.4] font-semibold md:text-3xl lg:mb-4 lg:text-4xl">
              Email
            </h3>
            <a className="underline" href="#">
              <EMAIL>
            </a>
          </div>
          <div>
            <div className="mb-5 lg:mb-6">
              <BiPhone className="size-12" />
            </div>
            <h3 className="mb-3 text-2xl leading-[1.4] font-semibold md:text-3xl lg:mb-4 lg:text-4xl">
              Telefoon
            </h3>
            <a className="underline" href="#">
              +32 XXX
            </a>
          </div>
          <div>
            <div className="mb-5 lg:mb-6">
              <BiMap className="size-12" />
            </div>
            <h3 className="mb-3 text-2xl leading-[1.4] font-semibold md:text-3xl lg:mb-4 lg:text-4xl">
              Kantoor
            </h3>
            <a className="underline" href="#">
              Kruisvest 5B, 8000 Brugge
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}

"use client";

import { Button } from "@relume_io/relume-ui";
import React from "react";

export function Cta13() {
  return (
    <section id="relume" className="relative px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container grid grid-rows-1 items-start gap-y-5 md:grid-cols-2 md:gap-x-12 md:gap-y-8 lg:gap-x-20 lg:gap-y-16">
        <div>
          <h1 className="text-5xl font-semibold md:text-7xl lg:text-8xl">
            Onze visie
          </h1>
        </div>
        <div>
          <p className="md:text-md">
            Bij Story Forward geloven we niet in oppervlakkige PR, maar in SR:
            Sustainable Relations. Geen holle slogans of vluchtige campagnes,
            wel het uitbouwen van duurzame, geloofwaardige relaties tussen jouw
            organisatie en je stakeholders. Wij helpen je jouw verhaal
            authentiek, boeiend en relevant te vertellen. Zo brengen onze
            experts jouw verhaal met precisie naar de juiste mensen op het
            juiste moment en via de juiste kanalen. Zodat het ook echt vruchten
            afwerpt. Of het nu gaat over strikte duurzaamheidscommunicatie, een
            maatschappelijk issue of een complex dossier, we willen een
            sparringpartner voor je zijn. Eentje die werkt vanuit het hoofd én
            het hart, maar zeker ook handen aan z’n lijf heeft. Daarvoor staat
            een straf team van jong talent, ervaren experten met een stevige
            rugzak, en betrouwbare en trouwe partners voor jou ter beschikking.
          </p>
          <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
            <Button title="Ontdek meer">Ontdek meer</Button>
            <Button title="Contacteer ons" variant="secondary">
              Contacteer ons
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}

"use client";

import { motion } from "framer-motion";
import React from "react";
import { Button1 } from "./Button1";

export function Layout394() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28 bg-background">
      <div className="container">
        <motion.div
          className="mx-auto mb-12 w-full max-w-lg text-center md:mb-18 lg:mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h1 className="mb-5 text-5xl font-semibold md:mb-6 md:text-7xl lg:text-8xl text-text-primary">
            Ons ecosysteem
          </h1>
        </motion.div>
        <motion.div
          className="grid auto-cols-fr grid-cols-1 gap-6 md:gap-8 lg:grid-cols-3"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          variants={{
            hidden: {},
            visible: {
              transition: {
                staggerChildren: 0.2
              }
            }
          }}
        >
          <motion.div
            className="flex flex-col border border-border rounded-lg overflow-hidden bg-background-primary hover:bg-background-secondary transition-all duration-300"
            variants={{
              hidden: { opacity: 0, y: 30 },
              visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
            }}
          >
            <div className="flex flex-1 flex-col justify-between p-6 md:p-8 lg:p-10">
              <div className="mb-6">
                <img
                  src="/brand/forestforward/full_logo_cut.png"
                  alt="Forest Forward Logo"
                  className="h-12 md:h-14 lg:h-16 w-auto object-contain mb-4"
                />
                <h2 className="mb-4 text-2xl font-semibold md:text-3xl md:leading-[1.3] lg:text-4xl text-text-primary">
                  DOE
                </h2>
                <p className="text-text-secondary leading-relaxed">
                  Bedrijfsbossen &#x2022; Schoolbossen &#x2022; Voedselbossen &#x2022;
                  Natuuropwaardering &#x2022; Dakboerderij
                </p>
              </div>

              <div className="mt-auto">
                <Button1
                  href="/forestforward"
                  variant="filled"
                  size="md"
                  className="w-full bg-link hover:bg-link-primary text-text-alternative"
                >
                  Ontdek Forest Forward
                </Button1>
              </div>
            </div>
          </motion.div>
          <motion.div
            className="flex flex-col border border-border rounded-lg overflow-hidden bg-background-primary hover:bg-background-secondary transition-all duration-300"
            variants={{
              hidden: { opacity: 0, y: 30 },
              visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
            }}
          >
            <div className="flex flex-1 flex-col justify-between p-6 md:p-8 lg:p-10">
              <div className="mb-6">
                <img
                  src="/brand/storyforward/full_logo_cut.png"
                  alt="Story Forward Logo"
                  className="h-12 md:h-14 lg:h-16 w-auto object-contain mb-4"
                />
                <h2 className="mb-4 text-2xl font-semibold md:text-3xl md:leading-[1.3] lg:text-4xl text-text-primary">
                  INSPIREER
                </h2>
                <p className="text-text-secondary leading-relaxed">
                  Storytelling &#x2022; Mediarelaties &#x2022; Strategische communicatie &#x2022;
                  Trainingen &#x2022; Branding &#x2022; Webdevelopment
                </p>
              </div>

              <div className="mt-auto">
                <Button1
                  href="/storyforward"
                  variant="filled"
                  size="md"
                  className="w-full bg-link hover:bg-link-primary text-text-alternative"
                >
                  Ontdek Story Forward
                </Button1>
              </div>
            </div>
          </motion.div>
          <motion.div
            className="flex flex-col border border-border rounded-lg overflow-hidden bg-background-primary hover:bg-background-secondary transition-all duration-300"
            variants={{
              hidden: { opacity: 0, y: 30 },
              visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
            }}
          >
            <div className="flex flex-1 flex-col justify-between p-6 md:p-8 lg:p-10">
              <div className="mb-6">
                <img
                  src="/brand/lagom/full_logo_cut.png"
                  alt="Lagom Logo"
                  className="h-12 md:h-14 lg:h-16 w-auto object-contain mb-4"
                />
                <h2 className="mb-4 text-2xl font-semibold md:text-3xl md:leading-[1.3] lg:text-4xl text-text-primary">
                  VERBIND
                </h2>
                <p className="text-text-secondary leading-relaxed">
                  Impact events &#x2022; Familiedagen &#x2022; Workshops &#x2022; Teambuildings &#x2022;
                  Keynotes
                </p>
              </div>

              <div className="mt-auto">
                <Button1
                  href="/lagom"
                  variant="filled"
                  size="md"
                  className="w-full bg-link hover:bg-link-primary text-text-alternative"
                >
                  Ontdek Lagom
                </Button1>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}

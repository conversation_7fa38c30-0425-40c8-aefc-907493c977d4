"use client";

import React from "react";

export function Header19() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="grid grid-cols-1 gap-x-20 gap-y-12 md:gap-y-16 lg:grid-cols-2 lg:items-center">
          <div>
            <img
              src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
              className="w-full rounded-image object-cover"
              alt="Relume placeholder image"
            />
          </div>
          <div>
            <h1 className="mb-5 text-6xl font-semibold md:mb-6 md:text-9xl lg:text-10xl">
              Trainingen & workshops
            </h1>
            <p className="md:text-md">
              Zoek je een specifieke training om jouw communicatievaardigheden
              naar een hoger niveau te tillen? Wij bieden op maat gemaakte
              workshops aan, dus afgestemd op jouw behoeften. Of het nu gaat om
              een korte dry run-sessie ter voorbereiding van een belangrijk
              interview of een volledige dagtraining – wij zorgen dat je
              beslagen op het ijs komt. We hebben hiervoor in-house expertise en
              werken samen met gedreven externe partners. Onze trainingen vinden
              plaats bij jou op locatie of bij ons op kantoor.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
